# FK-Steering (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) in Boltz 확산 모델: 종합 가이드

## 목차
1. [소개 및 개요](#1-소개-및-개요)
2. [이론적 기반](#2-이론적-기반)
3. [구현 분석](#3-구현-분석)
4. [실제 예시](#4-실제-예시)
5. [Guidance 시스템과의 통합](#5-guidance-시스템과의-통합)

---

## 1. 소개 및 개요

### 1.1 FK-Steering이란?

**FK-Steering (Feynman-<PERSON><PERSON>eering)**은 Boltz 확산 모델에서 사용되는 고급 샘플링 기법으로, **에너지 기반 입자 필터링(particle filtering)**과 **기울기 기반 guidance**를 결합하여 물리적으로 타당한 분자 구조를 생성합니다.

### 1.2 표준 확산 샘플링과의 차이점

**표준 확산 모델**:
```
x_{t-1} = μ(x_t, t) + σ(t) · ε    # 단순 노이즈 제거
```

**FK-Steering이 적용된 Boltz**:
```
1. x_{t-1} = μ(x_t, t) + σ(t) · ε     # 기본 denoising
2. x_{t-1} ← x_{t-1} - α∇E(x_{t-1})   # Gradient guidance
3. x_{t-1} ← Resample(x_{t-1}, w_E)   # Energy-based resampling
```

### 1.3 Guidance Weight 시스템과의 연결

FK-Steering은 이전에 분석한 guidance weight 시스템과 밀접하게 연결되어 있습니다:

- **`guidance_weight`**: 기울기 기반 구조 수정의 강도 조절
- **`resampling_weight`**: 에너지 기반 입자 필터링의 강도 조절
- **`fk_lambda`**: FK-Steering의 온도 매개변수

---

## 2. 이론적 기반

### 2.1 Feynman-Kac 과정의 수학적 정의

Feynman-Kac 과정은 확률적 미분방정식과 편미분방정식을 연결하는 수학적 프레임워크입니다:

```
dX_t = μ(X_t, t)dt + σ(X_t, t)dW_t    # 확률적 동역학
E[f(X_T)] = ∫ f(x)p(x,T)dx           # 기댓값 계산
```

### 2.2 에너지 기반 Resampling 이론

FK-Steering에서 핵심은 **Boltzmann 분포**를 따르는 에너지 기반 resampling입니다:

**Resampling Weight 계산**:
```
log_G_t = E_{t-1} - E_t                    # 에너지 개선도
w_t^i = exp(λ · log_G_t^i)                 # Boltzmann factor
P(선택) = w_t^i / Σ_j w_t^j               # 정규화된 확률
```

**물리적 의미**:
- `log_G_t > 0`: 에너지가 감소 (좋은 구조) → 높은 선택 확률
- `log_G_t < 0`: 에너지가 증가 (나쁜 구조) → 낮은 선택 확률
- `λ` (fk_lambda): 온도 매개변수, 높을수록 에너지 차이에 민감

### 2.3 Guidance Weight vs Resampling Weight

**수학적 관계**:

1. **Guidance Weight** (결정론적 수정):
   ```
   x_new = x_old - α · guidance_weight · ∇E(x_old)
   ```

2. **Resampling Weight** (확률적 선택):
   ```
   P(x_i 선택) ∝ exp(resampling_weight · ΔE_i)
   ```

**상호 보완적 역할**:
- **Guidance**: 각 입자를 더 나은 방향으로 이동
- **Resampling**: 나쁜 입자를 제거하고 좋은 입자를 복제

---

## 3. 구현 분석

### 3.1 FK-Steering 매개변수 설정

```python
@dataclass
class BoltzSteeringParams:
    """FK-steering configuration in Boltz."""
    fk_steering: bool = True           # FK resampling 활성화
    num_particles: int = 3             # 입자 수
    fk_lambda: float = 4.0             # 온도 매개변수
    fk_resampling_interval: int = 3    # Resampling 주기
    guidance_update: bool = True       # Gradient guidance 활성화
    num_gd_steps: int = 20            # Gradient descent 단계 수
```

### 3.2 코드 흐름 분석

#### 3.2.1 초기화 단계

```python
def sample(self, atom_mask, steering_args=None, **kwargs):
    potentials = get_potentials()
    
    if steering_args["fk_steering"]:
        # 입자 수만큼 multiplicity 증가
        multiplicity = multiplicity * steering_args["num_particles"]
        
        # 에너지 궤적 추적을 위한 텐서 초기화
        energy_traj = torch.empty((multiplicity, 0), device=self.device)
        
        # 초기 resampling weights (균등 분포)
        resample_weights = torch.ones(multiplicity, device=self.device).reshape(
            -1, steering_args["num_particles"]
        )
```

#### 3.2.2 에너지 계산 및 Resampling

```python
# FK resampling 조건 확인
if (step_idx % steering_args["fk_resampling_interval"] == 0 and noise_var > 0) or step_idx == num_sampling_steps - 1:
    
    # 1. 총 에너지 계산
    energy = torch.zeros(multiplicity, device=self.device)
    for potential in potentials:
        parameters = potential.compute_parameters(steering_t)
        if parameters["resampling_weight"] > 0:
            component_energy = potential.compute(
                atom_coords_denoised, feats, parameters
            )
            energy += parameters["resampling_weight"] * component_energy
    
    # 2. 에너지 궤적에 추가
    energy_traj = torch.cat((energy_traj, energy.unsqueeze(1)), dim=1)
    
    # 3. log_G 값 계산 (에너지 개선도)
    if step_idx == 0:
        log_G = -1 * energy  # 첫 번째 단계: 음의 에너지
    else:
        log_G = energy_traj[:, -2] - energy_traj[:, -1]  # 에너지 차이
    
    # 4. Likelihood difference 계산 (guidance와의 상호작용)
    if steering_args["guidance_update"] and noise_var > 0:
        ll_difference = (eps**2 - (eps + scaled_guidance_update)**2).sum(dim=(-1, -2)) / (2 * noise_var)
    else:
        ll_difference = torch.zeros_like(energy)
    
    # 5. Resampling weights 계산
    resample_weights = F.softmax(
        (ll_difference + steering_args["fk_lambda"] * log_G).reshape(-1, steering_args["num_particles"]),
        dim=1,
    )
```

#### 3.2.3 입자 Resampling 실행

```python
# Multinomial sampling을 통한 입자 선택
resample_indices = torch.multinomial(
    resample_weights,
    resample_weights.shape[1] if step_idx < num_sampling_steps - 1 else 1,
    replacement=True,
) + resample_weights.shape[1] * torch.arange(
    resample_weights.shape[0], device=resample_weights.device
).unsqueeze(-1)

# 선택된 입자들로 좌표 업데이트
atom_coords = atom_coords[resample_indices.flatten()]
```

### 3.3 핵심 수학적 연산

**에너지 개선도 계산**:
```python
log_G = energy_previous - energy_current  # 양수면 개선, 음수면 악화
```

**Boltzmann 가중치**:
```python
weights = exp(fk_lambda * log_G)  # 높은 log_G → 높은 가중치
```

**확률적 선택**:
```python
P(입자_i) = weights_i / sum(weights)  # 정규화된 확률
```

---

## 4. 실제 예시

### 4.1 Potential별 Weight 설정 예시

```python
def get_potentials(debug_enabled=True):
    potentials = [
        # 높은 빈도, 중요한 제약조건
        PoseBustersPotential(
            parameters={
                "guidance_interval": 1,      # 매 step 적용
                "guidance_weight": 1.0,      # 강한 gradient guidance
                "resampling_weight": 0.1,    # 약한 resampling
            }
        ),
        
        # 계산 비용이 높은 제약조건
        VDWOverlapPotential(
            parameters={
                "guidance_interval": 5,      # 5 step마다 적용
                "guidance_weight": PiecewiseStepFunction(
                    thresholds=[0.4], values=[0.125, 0.0]  # t=0.4 이후 비활성화
                ),
                "resampling_weight": PiecewiseStepFunction(
                    thresholds=[0.6], values=[0.01, 0.0]   # t=0.6 이후 비활성화
                ),
            }
        ),
        
        # 시간에 따라 강도가 증가하는 제약조건
        NMRDistancePotential(
            parameters={
                "guidance_interval": 1,
                "guidance_weight": ExponentialInterpolation(
                    start=0.05, end=0.15, alpha=-2.0  # 시간에 따라 증가
                ),
                "resampling_weight": 1.0,    # 일정한 resampling 강도
            }
        ),
    ]
```

### 4.2 성능 최적화 전략

**1. 적응적 Resampling 주기**:
```python
# 초기 단계: 자주 resampling (다양성 유지)
# 후기 단계: 덜 자주 resampling (수렴 안정성)
fk_resampling_interval = 3 if t > 0.5 else 1
```

**2. 온도 스케줄링**:
```python
# 시간에 따른 fk_lambda 조절
fk_lambda_t = fk_lambda_base * (1 - t)  # 시간이 지날수록 감소
```

### 4.3 계산 복잡도 분석

**메모리 사용량**:
- 기본 샘플링: `O(N × atoms × 3)`
- FK-Steering: `O(N × num_particles × atoms × 3)`

**계산 시간**:
- 에너지 계산: `O(num_particles × num_potentials)`
- Resampling: `O(num_particles × log(num_particles))`
- 전체 오버헤드: 약 2-3배 증가

---

## 5. Guidance 시스템과의 통합

### 5.1 3단계 통합 프로세스

Boltz의 FK-Steering은 다음 3단계로 구성됩니다:

```python
for step_idx in range(num_sampling_steps):
    # 1단계: 표준 Denoising
    atom_coords_denoised = self.denoise_step(atom_coords_noisy, sigma_t)
    
    # 2단계: Gradient-based Guidance
    if steering_args["guidance_update"]:
        guidance_update = torch.zeros_like(atom_coords_denoised)
        for guidance_step in range(steering_args["num_gd_steps"]):
            energy_gradient = torch.zeros_like(atom_coords_denoised)
            for potential in potentials:
                parameters = potential.compute_parameters(steering_t)
                if parameters["guidance_weight"] > 0:
                    energy_gradient += parameters["guidance_weight"] * potential.compute_gradient(
                        atom_coords_denoised + guidance_update, feats, parameters
                    )
            guidance_update -= energy_gradient  # Gradient descent
        atom_coords_denoised += guidance_update
    
    # 3단계: FK Resampling
    if steering_args["fk_steering"] and (step_idx % steering_args["fk_resampling_interval"] == 0):
        # 에너지 계산 및 resampling (위에서 설명한 과정)
        atom_coords_denoised = resample_particles(atom_coords_denoised, energy_weights)
```

### 5.2 각 메커니즘의 적용 시점

**Guidance Update**:
- **언제**: 매 denoising step 후 (마지막 step 제외)
- **목적**: 제약 조건을 더 잘 만족하는 방향으로 구조 수정
- **방법**: 에너지 기울기를 따라 gradient descent

**FK Resampling**:
- **언제**: `fk_resampling_interval`마다 또는 마지막 step
- **목적**: 높은 에너지 구조 제거, 낮은 에너지 구조 복제
- **방법**: Boltzmann 분포 기반 확률적 선택

### 5.3 상호 작용 메커니즘

**Likelihood Difference의 역할**:
```python
ll_difference = (eps**2 - (eps + scaled_guidance_update)**2).sum(dim=(-1, -2)) / (2 * noise_var)
```

이 항은 guidance update가 확산 과정의 확률 분포에 미치는 영향을 측정하여, resampling weights 계산에 반영합니다.

**최종 Resampling Weight**:
```python
total_weight = ll_difference + fk_lambda * log_G
resample_weights = softmax(total_weight)
```

이를 통해 **에너지 개선도**와 **확률적 일관성**을 모두 고려한 입자 선택이 가능합니다.

---

---

## 6. 고급 주제 및 최적화

### 6.1 적응적 FK-Steering

**동적 매개변수 조정**:
```python
def adaptive_fk_lambda(t, energy_variance):
    """시간과 에너지 분산에 따른 적응적 fk_lambda 조정"""
    base_lambda = 4.0
    variance_factor = min(2.0, energy_variance / 10.0)  # 분산이 클수록 강한 선택
    time_factor = 1.0 + t  # 시간이 지날수록 강한 선택
    return base_lambda * variance_factor * time_factor
```

**에너지 기반 Resampling 주기 조정**:
```python
def adaptive_resampling_interval(energy_improvement_rate):
    """에너지 개선 속도에 따른 resampling 주기 조정"""
    if energy_improvement_rate > 0.1:  # 빠른 개선
        return 5  # 덜 자주 resampling
    elif energy_improvement_rate < 0.01:  # 느린 개선
        return 1  # 자주 resampling
    else:
        return 3  # 기본값
```

### 6.2 메모리 및 계산 최적화

**배치 처리 최적화**:
```python
# 큰 시스템의 경우 배치 단위로 처리
max_parallel_samples = min(multiplicity, available_memory // memory_per_sample)
for batch_start in range(0, multiplicity, max_parallel_samples):
    batch_end = min(batch_start + max_parallel_samples, multiplicity)
    batch_coords = atom_coords[batch_start:batch_end]
    # FK-steering 적용
```

**선택적 Potential 활성화**:
```python
def selective_potential_activation(t, computational_budget):
    """계산 예산에 따른 potential 선택적 활성화"""
    if computational_budget == "low":
        # 중요한 potential만 활성화
        active_potentials = ["PoseBustersPotential", "ConnectionsPotential"]
    elif computational_budget == "medium":
        # 중간 중요도까지 활성화
        active_potentials = ["PoseBustersPotential", "ConnectionsPotential", "ChiralAtomPotential"]
    else:  # high budget
        # 모든 potential 활성화
        active_potentials = "all"

    return active_potentials
```

### 6.3 디버깅 및 모니터링

**에너지 궤적 시각화**:
```python
def plot_energy_trajectory(energy_traj, step_indices):
    """FK-steering 과정에서 에너지 변화 시각화"""
    import matplotlib.pyplot as plt

    plt.figure(figsize=(12, 6))
    for particle_idx in range(energy_traj.shape[0]):
        plt.plot(step_indices, energy_traj[particle_idx], alpha=0.7,
                label=f'Particle {particle_idx}')

    plt.xlabel('Diffusion Step')
    plt.ylabel('Total Energy')
    plt.title('Energy Trajectory during FK-Steering')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.show()
```

**Resampling 효율성 측정**:
```python
def measure_resampling_efficiency(resample_weights):
    """Resampling의 효율성 측정 (Effective Sample Size)"""
    ess = 1.0 / torch.sum(resample_weights**2, dim=1)
    max_ess = resample_weights.shape[1]  # num_particles
    efficiency = ess / max_ess
    return efficiency.mean().item()
```

---

## 7. 실제 사용 사례 및 베스트 프랙티스

### 7.1 단백질 구조 예측에서의 FK-Steering

**설정 예시**:
```python
# 큰 단백질 (>500 residues)
steering_args = {
    "fk_steering": True,
    "num_particles": 5,           # 더 많은 입자로 다양성 확보
    "fk_lambda": 3.0,             # 적당한 선택 압력
    "fk_resampling_interval": 5,  # 계산 비용 고려
    "guidance_update": True,
    "num_gd_steps": 15,           # 적당한 refinement
}

# 작은 단백질 (<100 residues)
steering_args = {
    "fk_steering": True,
    "num_particles": 3,           # 기본 입자 수
    "fk_lambda": 4.0,             # 강한 선택 압력
    "fk_resampling_interval": 3,  # 자주 resampling
    "guidance_update": True,
    "num_gd_steps": 20,           # 더 정밀한 refinement
}
```

### 7.2 약물-표적 복합체 예측

**특별 고려사항**:
```python
# 약물-표적 상호작용에 특화된 설정
def drug_target_fk_config(binding_site_size):
    """결합 부위 크기에 따른 FK-steering 설정"""
    if binding_site_size == "small":  # <10 residues
        return {
            "fk_lambda": 5.0,         # 매우 강한 선택 압력
            "fk_resampling_interval": 2,  # 자주 resampling
            "guidance_weight_multiplier": 1.5,  # 강한 guidance
        }
    elif binding_site_size == "large":  # >20 residues
        return {
            "fk_lambda": 3.0,         # 적당한 선택 압력
            "fk_resampling_interval": 4,  # 덜 자주 resampling
            "guidance_weight_multiplier": 1.0,  # 기본 guidance
        }
```

### 7.3 문제 해결 가이드

**일반적인 문제들**:

1. **에너지가 수렴하지 않는 경우**:
   ```python
   # fk_lambda 값을 줄여서 선택 압력 완화
   fk_lambda = 2.0  # 기본값 4.0에서 감소

   # resampling 주기를 늘려서 안정성 확보
   fk_resampling_interval = 5  # 기본값 3에서 증가
   ```

2. **다양성이 부족한 경우**:
   ```python
   # 입자 수 증가
   num_particles = 5  # 기본값 3에서 증가

   # guidance weight 감소
   guidance_weight_scale = 0.8  # 모든 guidance weight에 곱함
   ```

3. **계산 시간이 너무 긴 경우**:
   ```python
   # 선택적 potential 활성화
   active_potentials = ["PoseBustersPotential", "ConnectionsPotential"]

   # resampling 주기 증가
   fk_resampling_interval = 10

   # gradient descent 단계 수 감소
   num_gd_steps = 10
   ```

---

## 8. 이론적 확장 및 연구 방향

### 8.1 FK-Steering의 이론적 정당성

**수렴성 보장**:
FK-Steering은 다음 조건 하에서 올바른 분포로 수렴함이 이론적으로 보장됩니다:

1. **Detailed Balance**:
   ```
   π(x)P(x→y) = π(y)P(y→x)
   ```

2. **Ergodicity**: 모든 상태가 유한한 시간 내에 도달 가능

3. **Aperiodicity**: 주기적 행동 없음

### 8.2 향후 연구 방향

**1. 적응적 Potential 가중치**:
```python
def adaptive_potential_weights(energy_landscape_analysis):
    """에너지 지형 분석에 기반한 적응적 가중치 조정"""
    # 에너지 장벽이 높은 영역에서는 guidance 강화
    # 평평한 영역에서는 resampling 강화
    pass
```

**2. 다중 스케일 FK-Steering**:
```python
def multiscale_fk_steering(resolution_levels):
    """다중 해상도에서의 계층적 FK-steering"""
    # 거친 해상도에서 전역 구조 결정
    # 세밀한 해상도에서 지역 구조 정제
    pass
```

**3. 기계학습 기반 매개변수 최적화**:
```python
def ml_optimized_fk_params(structure_type, target_accuracy):
    """기계학습으로 최적화된 FK-steering 매개변수"""
    # 구조 유형과 목표 정확도에 따른 자동 매개변수 선택
    pass
```

---

## 결론

FK-Steering은 Boltz 확산 모델이 단순한 생성 모델을 넘어서 물리적으로 정확한 분자 구조를 생성할 수 있게 하는 핵심 기술입니다. **에너지 기반 입자 필터링**과 **기울기 기반 guidance**의 결합을 통해, 확산 과정에서 물리적 제약 조건을 효과적으로 만족시키면서도 구조적 다양성을 유지할 수 있습니다.

### 핵심 요점 정리

1. **이론적 기반**: Feynman-Kac 과정과 Boltzmann 분포에 기반한 수학적으로 엄밀한 프레임워크
2. **실용적 구현**: 계산 효율성과 물리적 정확성의 균형을 맞춘 실용적 알고리즘
3. **유연한 설정**: 다양한 분자 시스템과 계산 환경에 맞춘 적응적 매개변수 조정
4. **확장 가능성**: 새로운 potential과 제약 조건의 쉬운 통합

이 시스템의 성공은 적절한 매개변수 조정(`fk_lambda`, `resampling_interval`, `guidance_weight` 등)과 각 potential의 특성에 맞는 스케줄링 전략에 크게 의존합니다. 향후 연구를 통해 더욱 지능적이고 효율적인 FK-Steering 시스템의 개발이 기대됩니다.
