from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Optional, Union

import torch

from boltz.data import const
from boltz.model.potentials.schedules import (
    ExponentialInterpolation,
    ParameterSchedule,
    PiecewiseStepFunction,
)


class Potential(ABC):
    def __init__(
        self,
        parameters: Optional[
            dict[str, Union[ParameterSchedule, float, int, bool]]
        ] = None,
        debug_enabled: bool = False,
    ):
        """
        Initialize Potential with optional logging capabilities.
        
        Parameters
        ----------
        parameters : dict, optional
            Potential parameters including schedules and constants
        debug_enabled : bool, default=False
            Enable detailed debug logging for development
        """
        self.parameters = parameters
        
        # Logging configuration
        self.debug_enabled = debug_enabled
        
        # Statistics tracking
        self.call_count = 0
        self.gradient_call_count = 0
        
        # Configuration logging control (to prevent repeated config info)
        self._config_logged = False
        
        # Get potential name for logging
        self._potential_name = self.__class__.__name__
    
    def _log_debug(self, message: str, end: bool = True) -> None:
        """Log debug message if debug logging is enabled."""
        if self.debug_enabled:
            print(f"[{self._potential_name}] {message}", end="\n" if end else " ")
    
    def _log_config(self, message: str) -> None:
        """Log configuration message only once."""
        if self.debug_enabled and not self._config_logged:
            print(f"[{self._potential_name}][CONFIG] {message}")

    
    def _mark_config_logged(self) -> None:
        """Mark configuration as logged to prevent repeated output."""
        self._config_logged = True
    
    def enable_debug(self) -> None:
        """Enable debug logging."""
        self.debug_enabled = True
        self._log_debug("Debug logging enabled")
    
    def disable_debug(self) -> None:
        """Disable debug logging."""
        if self.debug_enabled:
            self._log_debug("Debug logging disabled")
        self.debug_enabled = False
    
    def get_stats(self) -> dict:
        """
        Get current statistics.
        
        Returns
        -------
        dict
            Dictionary containing call statistics and logging status
        """
        return {
            "potential_name": self._potential_name,
            "compute_calls": self.call_count,
            "gradient_calls": self.gradient_call_count,
            "debug_enabled": self.debug_enabled,
            "config_logged": self._config_logged
        }

    def compute(self, coords, feats, parameters):
        # Increment call counter and log if enabled
        self.call_count += 1
        
        # if self.debug_enabled:
        #     self._log_debug(f"compute() called #{self.call_count} ===================")
        #     # self._log_debug(f"  coords: {coords.shape}")
        #     # self._log_debug(f"  batch size: {coords.shape[0]}, atoms: {coords.shape[1]}")
        
        # Original compute logic
        index, args, com_args = self.compute_args(feats, parameters)

        if index.shape[1] == 0:
            #if self.debug_enabled: print(f"No constraints found, returning zero energy")            
            return torch.zeros(coords.shape[:-2], device=coords.device)

        if com_args is not None:
            com_index, atom_pad_mask = com_args
            unpad_com_index = com_index[atom_pad_mask]
            unpad_coords = coords[..., atom_pad_mask, :]
            coords = torch.zeros(
                (*unpad_coords.shape[:-2], unpad_com_index.max() + 1, 3),
                device=coords.device,
            ).scatter_reduce(
                -2,
                unpad_com_index.unsqueeze(-1).expand_as(unpad_coords),
                unpad_coords,
                "mean",
            )
        
        value = self.compute_variable(coords, index, compute_gradient=False)
        energy = self.compute_function(value, *args)
        result = energy.sum(dim=-1)
        
        # Log energy statistics if enabled
        if self.debug_enabled and result is not None:
            total_energy = result.sum().item()            
            if total_energy > 1e-6: # Only log if there's actual energy (not just zero)
                self._log_debug(f"compute() called #{self.call_count} ===================")
                self._log_debug(
                    f" Total energy: {total_energy:.2f} / mean={result.mean().item():.3f}, "
                    f"min={result.min().item():.3f}, max={result.max().item():.3f}",
                )
            else:
                self._log_debug(f" {total_energy:.2f} No significant energy")
        
        # Calculate total energy for runtime info and first activity detection
        total_energy = result.sum().item() if result is not None else 0.0
        
        # Log meaningful runtime info periodically (values that actually change)
        if self.debug_enabled and result is not None and self.call_count % 50 == 1:
            active_constraints = (result > 1e-6).sum().item()  # Count constraints with significant energy
            max_energy = result.max().item()
            self._log_debug(f"Runtime: Total_energy={total_energy:.2f}, Active_violations={active_constraints}, Max_violation={max_energy:.4f}")
        
        # Always log if this is the first time this potential produces non-zero energy
        if (self.debug_enabled and result is not None and not hasattr(self, '_first_nonzero_logged') 
            and total_energy > 1e-6):
            self._log_debug(f"🎯 FIRST ACTIVITY: {self._potential_name} now contributing energy!")
            self._first_nonzero_logged = True
        
        return result

    def compute_gradient(self, coords, feats, parameters):
        # Increment gradient call counter
        self.gradient_call_count += 1
        
        # Original gradient computation logic
        index, args, com_args = self.compute_args(feats, parameters)
        if com_args is not None:
            com_index, atom_pad_mask = com_args
        else:
            com_index, atom_pad_mask = None, None

        if index.shape[1] == 0:
            # if self.debug_enabled and self.gradient_call_count % 10 == 1: # Log every 10th to reduce spam
            #     print("  No constraints for gradient, returning zero")
            return torch.zeros_like(coords)

        if com_index is not None:
            unpad_coords = coords[..., atom_pad_mask, :]
            unpad_com_index = com_index[atom_pad_mask]
            coords = torch.zeros(
                (*unpad_coords.shape[:-2], unpad_com_index.max() + 1, 3),
                device=coords.device,
            ).scatter_reduce(
                -2,
                unpad_com_index.unsqueeze(-1).expand_as(unpad_coords),
                unpad_coords,
                "mean",
            )
            com_counts = torch.bincount(com_index[atom_pad_mask])

        value, grad_value = self.compute_variable(coords, index, compute_gradient=True)
        energy, dEnergy = self.compute_function(value, *args, compute_derivative=True)

        grad_atom = torch.zeros_like(coords).scatter_reduce(
            -2,
            index.flatten(start_dim=0, end_dim=1)
            .unsqueeze(-1)
            .expand((*coords.shape[:-2], -1, 3)),
            dEnergy.tile(grad_value.shape[-3]).unsqueeze(-1)
            * grad_value.flatten(start_dim=-3, end_dim=-2),
            "sum",
        )

        if com_index is not None:
            grad_atom = grad_atom[..., com_index, :]

        # Log gradient statistics if enabled
        if self.debug_enabled and grad_atom is not None and self.gradient_call_count % 10 == 1:
            grad_norm = torch.norm(grad_atom).item()
            self._log_debug(f"compute_gradient() called #{self.gradient_call_count} gradient norm: {grad_norm:.4f}")

        return grad_atom

    def compute_parameters(self, t):
        if self.parameters is None:
            return None
        parameters = {
            name: parameter
            if not isinstance(parameter, ParameterSchedule)
            else parameter.compute(t)
            for name, parameter in self.parameters.items()
        }
        return parameters

    @abstractmethod
    def compute_function(self, value, *args, compute_derivative=False):
        raise NotImplementedError

    @abstractmethod
    def compute_variable(self, coords, index, compute_gradient=False):
        raise NotImplementedError

    @abstractmethod
    def compute_args(self, t, feats, **parameters):
        raise NotImplementedError

## -- Potentials -- ##
class FlatBottomPotential(Potential):
    def compute_function(
        self, value, k, lower_bounds, upper_bounds, compute_derivative=False
    ):
        if lower_bounds is None:
            lower_bounds = torch.full_like(value, float("-inf"))
        if upper_bounds is None:
            upper_bounds = torch.full_like(value, float("inf"))

        neg_overflow_mask = value < lower_bounds
        pos_overflow_mask = value > upper_bounds

        energy = torch.zeros_like(value)
        energy[neg_overflow_mask] = (k * (lower_bounds - value))[neg_overflow_mask]
        energy[pos_overflow_mask] = (k * (value - upper_bounds))[pos_overflow_mask]
        if not compute_derivative:
            return energy

        dEnergy = torch.zeros_like(value)
        dEnergy[neg_overflow_mask] = (
            -1 * k.expand_as(neg_overflow_mask)[neg_overflow_mask]
        )
        dEnergy[pos_overflow_mask] = (
            1 * k.expand_as(pos_overflow_mask)[pos_overflow_mask]
        )

        return energy, dEnergy


class HarmonicPotential(Potential):
    """    
    HarmonicPotential applies quadratic penalty proportional to deviation from equilibrium position.
    This class implements the harmonic oscillator energy function:
    E = 0.5 * k * (value - equilibrium)^2
    """
    
    def compute_function(
        self, value, k, equilibrium, compute_derivative=False
    ):
        """
        Compute harmonic potential energy and optionally its derivative.
        
        Parameters
        ----------
        value : torch.Tensor
            Current values of the collective variable (e.g., distances)
        k : torch.Tensor
            Force constants for each constraint
        equilibrium : torch.Tensor
            Equilibrium values for each constraint
        compute_derivative : bool, default=False
            Whether to compute derivative of energy w.r.t. value
            
        Returns
        -------
        torch.Tensor or tuple[torch.Tensor, torch.Tensor]
            Energy values, and optionally derivatives
        """
        # calculate r - r0
        deviation = value - equilibrium
        
        # Harmonic energy: E = 0.5 * k * (r - r0)^2
        energy = 0.5 * (k * deviation)**2
        
        if not compute_derivative:
            return energy
        
        # Derivative (gradient): dE/dr = k * (r - r0)
        dEnergy = k * deviation
        
        return energy, dEnergy


class DistancePotential(Potential):
    def compute_variable(self, coords, index, compute_gradient=False):
        r_ij = coords.index_select(-2, index[0]) - coords.index_select(-2, index[1])
        r_ij_norm = torch.linalg.norm(r_ij, dim=-1)
        r_ij_norm = torch.clamp(r_ij_norm, min=1e-6) # code modification: avoid division by zero
        r_hat_ij = r_ij / r_ij_norm.unsqueeze(-1)
        
        # print(f'min r_ij_norm: {torch.min(r_ij.flatten())}')
        # print(f'max r_ij_norm: {torch.max(r_ij.flatten())}')
        # print(f'mean r_ij_norm: {torch.mean(r_ij_norm.flatten())}')
        # print(f'std r_ij_norm: {torch.std(r_ij_norm.flatten())}')
        # print(f'isnan r_ij_norm: {torch.isnan(r_ij_norm).any()}')
        # print(f'isinf r_ij_norm: {torch.isinf(r_ij_norm).any()}')
        
        # Log gradient statistics if enabled
        if not compute_gradient:
            return r_ij_norm

        grad_i = r_hat_ij
        grad_j = -1 * r_hat_ij
        grad = torch.stack((grad_i, grad_j), dim=1)

        return r_ij_norm, grad


class AnglePotential(Potential):
    """
    Angle potential for computing angles between three atoms.

    This potential computes the angle formed by three atoms (atom1-atom2-atom3)
    where atom2 is the center atom. The angle is computed using vector dot product.

    Energy function: E = 0.5 * k * (θ - θ0)^2
    where θ is current angle, θ0 is equilibrium angle, k is force constant.
    """

    def compute_variable(self, coords, index, compute_gradient=False):
        """
        Compute angle between three atoms and optionally its gradient.

        Parameters
        ----------
        coords : torch.Tensor
            Atomic coordinates with shape (..., N_atoms, 3)
        index : torch.Tensor
            Indices of three atoms with shape (3, N_constraints)
            index[0]: atom1 indices, index[1]: atom2 indices (center), index[2]: atom3 indices
        compute_gradient : bool
            Whether to compute gradients

        Returns
        -------
        torch.Tensor or tuple
            If compute_gradient=False: angles with shape (..., N_constraints)
            If compute_gradient=True: (angles, gradients) where gradients has shape (..., 3, N_constraints, 3)
        """
        # Get coordinates of the three atoms
        coords_1 = coords.index_select(-2, index[0])  # atom1 coordinates
        coords_2 = coords.index_select(-2, index[1])  # atom2 coordinates (center)
        coords_3 = coords.index_select(-2, index[2])  # atom3 coordinates

        # Compute vectors from center atom to other atoms
        vec_21 = coords_1 - coords_2  # vector from atom2 to atom1
        vec_23 = coords_3 - coords_2  # vector from atom2 to atom3

        # Compute vector norms
        norm_21 = torch.linalg.norm(vec_21, dim=-1, keepdim=True)
        norm_23 = torch.linalg.norm(vec_23, dim=-1, keepdim=True)

        # Normalize vectors
        vec_21_normalized = vec_21 / (norm_21 + 1e-8)  # Add small epsilon for numerical stability
        vec_23_normalized = vec_23 / (norm_23 + 1e-8)

        # Compute cosine of angle using dot product
        cos_angle = torch.sum(vec_21_normalized * vec_23_normalized, dim=-1)

        # Clamp cosine to valid range [-1, 1] for numerical stability
        cos_angle = torch.clamp(cos_angle, -1.0 + 1e-6, 1.0 - 1e-6)

        # Compute angle in radians
        angle = torch.acos(cos_angle)

        if not compute_gradient:
            return angle

        # Compute gradients if requested
        # d(angle)/d(coords) for each of the three atoms

        # sin(angle) for gradient computation
        sin_angle = torch.sin(angle).unsqueeze(-1)  # Add dimension for broadcasting
        sin_angle = torch.clamp(sin_angle, 1e-6, None)  # Avoid division by zero

        # Gradient components
        # d(angle)/d(atom1)
        grad_1 = (vec_23_normalized - cos_angle.unsqueeze(-1) * vec_21_normalized) / (norm_21.squeeze(-1).unsqueeze(-1) * sin_angle)

        # d(angle)/d(atom3)
        grad_3 = (vec_21_normalized - cos_angle.unsqueeze(-1) * vec_23_normalized) / (norm_23.squeeze(-1).unsqueeze(-1) * sin_angle)

        # d(angle)/d(atom2) = -(grad_1 + grad_3) by chain rule
        grad_2 = -(grad_1 + grad_3)

        # Stack gradients: shape (..., 3, N_constraints, 3)
        grad = torch.stack((grad_1, grad_2, grad_3), dim=-3)

        return angle, grad


class DihedralPotential(Potential):
    def compute_variable(self, coords, index, compute_gradient=False):
        r_ij = coords.index_select(-2, index[0]) - coords.index_select(-2, index[1])
        r_kj = coords.index_select(-2, index[2]) - coords.index_select(-2, index[1])
        r_kl = coords.index_select(-2, index[2]) - coords.index_select(-2, index[3])

        n_ijk = torch.cross(r_ij, r_kj, dim=-1)
        n_jkl = torch.cross(r_kj, r_kl, dim=-1)

        r_kj_norm = torch.linalg.norm(r_kj, dim=-1)
        n_ijk_norm = torch.linalg.norm(n_ijk, dim=-1)
        n_jkl_norm = torch.linalg.norm(n_jkl, dim=-1)

        sign_phi = torch.sign(
            r_kj.unsqueeze(-2) @ torch.cross(n_ijk, n_jkl, dim=-1).unsqueeze(-1)
        ).squeeze(-1, -2)
        phi = sign_phi * torch.arccos(
            torch.clamp(
                (n_ijk.unsqueeze(-2) @ n_jkl.unsqueeze(-1)).squeeze(-1, -2)
                / (n_ijk_norm * n_jkl_norm),
                -1 + 1e-8,
                1 - 1e-8,
            )
        )

        if not compute_gradient:
            return phi

        a = (
            (r_ij.unsqueeze(-2) @ r_kj.unsqueeze(-1)).squeeze(-1, -2) / (r_kj_norm**2)
        ).unsqueeze(-1)
        b = (
            (r_kl.unsqueeze(-2) @ r_kj.unsqueeze(-1)).squeeze(-1, -2) / (r_kj_norm**2)
        ).unsqueeze(-1)

        grad_i = n_ijk * (r_kj_norm / n_ijk_norm**2).unsqueeze(-1)
        grad_l = -1 * n_jkl * (r_kj_norm / n_jkl_norm**2).unsqueeze(-1)
        grad_j = (a - 1) * grad_i - b * grad_l
        grad_k = (b - 1) * grad_l - a * grad_i
        grad = torch.stack((grad_i, grad_j, grad_k, grad_l), dim=1)
        return phi, grad


## ======================= ##
##   Specific potentials   ##
## ======================= ##
class AbsDihedralPotential(DihedralPotential):
    def compute_variable(self, coords, index, compute_gradient=False):
        if not compute_gradient:
            phi = super().compute_variable(
                coords, index, compute_gradient=compute_gradient
            )
            phi = torch.abs(phi)
            return phi

        phi, grad = super().compute_variable(
            coords, index, compute_gradient=compute_gradient
        )
        grad[(phi < 0)[..., None, :, None].expand_as(grad)] *= -1
        phi = torch.abs(phi)

        return phi, grad


class PoseBustersPotential(FlatBottomPotential, DistancePotential):
    def compute_args(self, feats, parameters):
        pair_index = feats["rdkit_bounds_index"][0] #(i, j, k) (i, j)
        lower_bounds = feats["rdkit_lower_bounds"][0].clone()
        upper_bounds = feats["rdkit_upper_bounds"][0].clone()
        bond_mask = feats["rdkit_bounds_bond_mask"][0]
        angle_mask = feats["rdkit_bounds_angle_mask"][0]

        # Log configuration info only on first call
        if not self._config_logged:
            num_constraints = pair_index.shape[1]
            num_bonds = bond_mask.sum().item()
            num_angles = angle_mask.sum().item()
            num_clashes = ((~bond_mask) * (~angle_mask)).sum().item()
            self._log_config(f"PoseBusters potential initialized")
            self._log_config(f"  Total constraints: {num_constraints}")
            self._log_config(f"  Bond constraints: {num_bonds}")
            self._log_config(f"  Angle constraints: {num_angles}")
            self._log_config(f"  Clash constraints: {num_clashes}")
            self._log_config(f"  Buffers - bond: {parameters['bond_buffer']:.3f}, angle: {parameters['angle_buffer']:.3f}, clash: {parameters['clash_buffer']:.3f}")
            self._log_config('-' * 50)
            
        lower_bounds[bond_mask * ~angle_mask] *= 1.0 - parameters["bond_buffer"]
        upper_bounds[bond_mask * ~angle_mask] *= 1.0 + parameters["bond_buffer"]
        lower_bounds[~bond_mask * angle_mask] *= 1.0 - parameters["angle_buffer"]
        upper_bounds[~bond_mask * angle_mask] *= 1.0 + parameters["angle_buffer"]
        lower_bounds[bond_mask * angle_mask] *= 1.0 - min(
            parameters["angle_buffer"], parameters["angle_buffer"]
        )
        upper_bounds[bond_mask * angle_mask] *= 1.0 + min(
            parameters["angle_buffer"], parameters["angle_buffer"]
        )
        lower_bounds[~bond_mask * ~angle_mask] *= 1.0 - parameters["clash_buffer"]
        upper_bounds[~bond_mask * ~angle_mask] = float("inf")

        k = torch.ones_like(lower_bounds)

        # Log final configuration info only on first call
        if not self._config_logged:
            finite_lower = lower_bounds[torch.isfinite(lower_bounds)]
            finite_upper = upper_bounds[torch.isfinite(upper_bounds)]
            if len(finite_lower) > 0:
                self._log_config(f"  Final lower bounds range: {finite_lower.min().item():.3f} - {finite_lower.max().item():.3f} Å")
            if len(finite_upper) > 0:
                self._log_config(f"  Final upper bounds range: {finite_upper.min().item():.3f} - {finite_upper.max().item():.3f} Å")
            self._mark_config_logged()
            

        # Note: PoseBusters runtime info is logged in base compute() method
        return pair_index, (k, lower_bounds, upper_bounds), None


class ConnectionsPotential(FlatBottomPotential, DistancePotential):
    def compute_args(self, feats, parameters):
        pair_index = feats["connected_atom_index"][0]
        
        # Log configuration info only on first call
        if not self._config_logged:
            num_connections = pair_index.shape[1]
            self._log_config(f"Connections potential initialized")
            self._log_config(f"  Connected atom pairs: {num_connections}")
            self._log_config(f"  Buffer distance: {parameters['buffer']:.3f} Å")
            self._mark_config_logged()
        
        lower_bounds = None
        upper_bounds = torch.full(
            (pair_index.shape[1],), parameters["buffer"], device=pair_index.device
        )
        k = torch.ones_like(upper_bounds)

        # Note: Connections runtime info is logged in base compute() method
        return pair_index, (k, lower_bounds, upper_bounds), None


class VDWOverlapPotential(FlatBottomPotential, DistancePotential):
    def compute_args(self, feats, parameters):
        atom_chain_id = (
            torch.bmm(
                feats["atom_to_token"].float(), feats["asym_id"].unsqueeze(-1).float()
            )
            .squeeze(-1)
            .long()
        )[0]
        atom_pad_mask = feats["atom_pad_mask"][0].bool()
        chain_sizes = torch.bincount(atom_chain_id[atom_pad_mask])
        single_ion_mask = (chain_sizes > 1)[atom_chain_id]

        vdw_radii = torch.zeros(
            const.num_elements, dtype=torch.float32, device=atom_chain_id.device
        )
        vdw_radii[1:119] = torch.tensor(
            const.vdw_radii, dtype=torch.float32, device=atom_chain_id.device
        )
        atom_vdw_radii = (
            feats["ref_element"].float() @ vdw_radii.unsqueeze(-1)
        ).squeeze(-1)[0]

        # Log configuration info only on first call
        if not self._config_logged:
            num_atoms = atom_chain_id.shape[0]
            num_chains = torch.unique(atom_chain_id[atom_pad_mask]).shape[0]
            self._log_config(f"VDW overlap potential initialized")
            self._log_config(f"  Total atoms: {num_atoms}, Active chains: {num_chains}")
            self._log_config(f"  Buffer parameter: {parameters['buffer']:.3f}")
            self._log_config('-' * 50)
        
        pair_index = torch.triu_indices(
            atom_chain_id.shape[0],
            atom_chain_id.shape[0],
            1,
            device=atom_chain_id.device,
        )

        pair_pad_mask = atom_pad_mask[pair_index].all(dim=0)
        pair_ion_mask = single_ion_mask[pair_index[0]] * single_ion_mask[pair_index[1]]

        num_chains = atom_chain_id.max() + 1
        connected_chain_index = feats["connected_chain_index"][0]
        connected_chain_matrix = torch.eye(
            num_chains, device=atom_chain_id.device, dtype=torch.bool
        )
        connected_chain_matrix[connected_chain_index[0], connected_chain_index[1]] = (
            True
        )
        connected_chain_matrix[connected_chain_index[1], connected_chain_index[0]] = (
            True
        )
        connected_chain_mask = connected_chain_matrix[
            atom_chain_id[pair_index[0]], atom_chain_id[pair_index[1]]
        ]

        pair_index = pair_index[
            :, pair_pad_mask * pair_ion_mask * ~connected_chain_mask
        ]

        lower_bounds = atom_vdw_radii[pair_index].sum(dim=0) * (
            1.0 - parameters["buffer"]
        )
        upper_bounds = None
        k = torch.ones_like(lower_bounds)

        # Log final configuration info only on first call
        if not self._config_logged:
            num_pairs = pair_index.shape[1]
            self._log_config(f"  Active VDW pairs: {num_pairs}")
            if num_pairs > 0:
                self._log_config(f"  VDW distance range: {lower_bounds.min().item():.3f} - {lower_bounds.max().item():.3f} Å")
            self._mark_config_logged()

        # Note: VDW runtime info is logged in base compute() method
        return pair_index, (k, lower_bounds, upper_bounds), None


class SymmetricChainCOMPotential(FlatBottomPotential, DistancePotential):
    def compute_args(self, feats, parameters):
        atom_chain_id = (
            torch.bmm(
                feats["atom_to_token"].float(), feats["asym_id"].unsqueeze(-1).float()
            )
            .squeeze(-1)
            .long()
        )[0]
        atom_pad_mask = feats["atom_pad_mask"][0].bool()
        chain_sizes = torch.bincount(atom_chain_id[atom_pad_mask])
        single_ion_mask = chain_sizes > 1

        pair_index = feats["symmetric_chain_index"][0]
        pair_ion_mask = single_ion_mask[pair_index[0]] * single_ion_mask[pair_index[1]]
        pair_index = pair_index[:, pair_ion_mask]
        lower_bounds = torch.full(
            (pair_index.shape[1],),
            parameters["buffer"],
            dtype=torch.float32,
            device=pair_index.device,
        )
        upper_bounds = None
        k = torch.ones_like(lower_bounds)        

        return (
            pair_index,
            (k, lower_bounds, upper_bounds),
            (atom_chain_id, atom_pad_mask),
        )


class StereoBondPotential(FlatBottomPotential, AbsDihedralPotential):
    def compute_args(self, feats, parameters):
        stereo_bond_index = feats["stereo_bond_index"][0]
        stereo_bond_orientations = feats["stereo_bond_orientations"][0].bool()

        lower_bounds = torch.zeros(
            stereo_bond_orientations.shape, device=stereo_bond_orientations.device
        )
        upper_bounds = torch.zeros(
            stereo_bond_orientations.shape, device=stereo_bond_orientations.device
        )
        lower_bounds[stereo_bond_orientations] = torch.pi - parameters["buffer"]
        upper_bounds[stereo_bond_orientations] = float("inf")
        lower_bounds[~stereo_bond_orientations] = float("-inf")
        upper_bounds[~stereo_bond_orientations] = parameters["buffer"]

        k = torch.ones_like(lower_bounds)

        return stereo_bond_index, (k, lower_bounds, upper_bounds), None


class ChiralAtomPotential(FlatBottomPotential, DihedralPotential):
    def compute_args(self, feats, parameters):
        chiral_atom_index = feats["chiral_atom_index"][0]
        chiral_atom_orientations = feats["chiral_atom_orientations"][0].bool()

        lower_bounds = torch.zeros(
            chiral_atom_orientations.shape, device=chiral_atom_orientations.device
        )
        upper_bounds = torch.zeros(
            chiral_atom_orientations.shape, device=chiral_atom_orientations.device
        )
        lower_bounds[chiral_atom_orientations] = parameters["buffer"]
        upper_bounds[chiral_atom_orientations] = float("inf")
        upper_bounds[~chiral_atom_orientations] = -1 * parameters["buffer"]
        lower_bounds[~chiral_atom_orientations] = float("-inf")

        k = torch.ones_like(lower_bounds)
        return chiral_atom_index, (k, lower_bounds, upper_bounds), None


class PlanarBondPotential(FlatBottomPotential, AbsDihedralPotential):
    def compute_args(self, feats, parameters):
        double_bond_index = feats["planar_bond_index"][0].T
        double_bond_improper_index = torch.tensor(
            [
                [1, 2, 3, 0],
                [4, 5, 0, 3],
            ],
            device=double_bond_index.device,
        ).T
        improper_index = (
            double_bond_index[:, double_bond_improper_index]
            .swapaxes(0, 1)
            .flatten(start_dim=1)
        )
        lower_bounds = None
        upper_bounds = torch.full(
            (improper_index.shape[1],),
            parameters["buffer"],
            device=improper_index.device,
        )
        k = torch.ones_like(upper_bounds)

        return improper_index, (k, lower_bounds, upper_bounds), None


## ===================== ##
##   Code Modification   ##
## ===================== ##
class MinDistancePotential(FlatBottomPotential, DistancePotential):
    """
    Minimum distance constraints between atom pairs.
    
    This potential enforces minimum distance constraints by applying
    a flat-bottom potential that penalizes distances below the specified
    minimum distance threshold.
    """
    
    def compute_args(self, feats, parameters):
        pair_index = feats["min_distance_atom_index"][0]
        min_distance_values = feats["min_distance_values"][0]
        lower_bounds = min_distance_values 
        upper_bounds = None
        k = torch.ones_like(lower_bounds) * 50

        return pair_index, (k, lower_bounds, upper_bounds), None


class NMRDistancePotential(FlatBottomPotential, DistancePotential):
    def compute(self, coords, feats, parameters):
        """Override compute to add NMR-specific runtime info."""
        # Call parent compute to get standard logging
        result = super().compute(coords, feats, parameters)
        
        # Add NMR-specific runtime information
        if (self.debug_enabled and result is not None and 
            hasattr(self, '_last_nmr_runtime_log') and 
            self.call_count - self._last_nmr_runtime_log >= 100):
            
            # Get actual distances for constraints
            index, args, com_args = self.compute_args(feats, parameters)
            if index.shape[1] > 0:
                # Compute actual distances
                r_ij = coords.index_select(-2, index[0]) - coords.index_select(-2, index[1])
                distances = torch.linalg.norm(r_ij, dim=-1)
                
                # Get bounds for comparison
                _, lower_bounds, upper_bounds = args
                
                # Analyze constraint violations
                lower_violations = (distances < lower_bounds).sum().item()
                upper_violations = (distances > upper_bounds[torch.isfinite(upper_bounds)]).sum().item() if torch.isfinite(upper_bounds).any() else 0
                
                avg_distance = distances.mean().item()
                distance_std = distances.std().item()
                
                self._log_debug(
                    f"NMR Analysis: Avg_dist={avg_distance:.3f}±{distance_std:.3f}Å, "
                    f"Lower_violations={lower_violations}, Upper_violations={upper_violations}"
                )
                
            self._last_nmr_runtime_log = self.call_count
        
        # Initialize the runtime log counter if not exists
        if not hasattr(self, '_last_nmr_runtime_log'):
            self._last_nmr_runtime_log = 0
            
        return result
    
    def compute_args(self, feats, parameters):
        """
        Compute arguments for NMR distance potential.
        
        Parameters
        ----------
        feats : dict
            Feature dictionary containing NMR distance constraints
        parameters : dict
            Potential parameters including buffers and weights
            
        Returns
        -------
        tuple
            (pair_index, (k, lower_bounds, upper_bounds), None)
        """
        # Check if NMR distance features exist
        if "nmr_distance_atom_index" not in feats:
            # if self.debug_enabled:
            #     self._log_debug("No NMR distance constraints found in features")
            # Return empty tensors if no constraints are available
            device = next(iter(feats.values())).device if feats else 'cpu'
            return torch.empty((2, 0), device=device), (torch.empty(0, device=device), torch.empty(0, device=device), torch.empty(0, device=device)), None
            
        pair_index = feats["nmr_distance_atom_index"][0]
        lower_bounds = feats["nmr_distance_lower_bounds"][0].clone()
        upper_bounds = feats["nmr_distance_upper_bounds"][0].clone()
        weights = feats["nmr_distance_weights"][0]
        
        # Log configuration info only on first call
        if not self._config_logged:
            num_constraints = pair_index.shape[1]
            self._log_config(f"Processing {num_constraints} NMR distance constraints")
            self._log_config(f"  Original lower bounds: {lower_bounds.min().item():.3f} - {lower_bounds.max().item():.3f} Å")
            self._log_config(f"  Original upper bounds: {upper_bounds.min().item():.3f} - {upper_bounds.max().item():.3f} Å")
            self._log_config(f"  Weights: {weights.min().item():.3f} - {weights.max().item():.3f}")
            self._log_config(f"  Applied buffers: lower={parameters['lower_buffer']:.3f}, upper={parameters['upper_buffer']:.3f}")
            self._log_config('-' * 50)
        # Apply buffer to bounds for soft constraints
        # Lower bounds are reduced by buffer percentage to allow some flexibility
        lower_bounds = lower_bounds * (1.0 - parameters["lower_buffer"])
        
        # Upper bounds are increased by buffer percentage, but handle infinite values
        finite_mask = torch.isfinite(upper_bounds)
        upper_bounds[finite_mask] = upper_bounds[finite_mask] * (1.0 + parameters["upper_buffer"])
        
        # Apply weights as force constants
        k = weights * parameters["base_force_constant"]
        
        # Log final configuration info only on first call
        if not self._config_logged:
            self._log_config(f"  Final force constants: {k.min().item():.3f} - {k.max().item():.3f}")
            self._log_config(f"  Final lower bounds: {lower_bounds.min().item():.3f} - {lower_bounds.max().item():.3f} Å")
            finite_upper = upper_bounds[finite_mask]
            if len(finite_upper) > 0:
                self._log_config(f"  Final upper bounds: {finite_upper.min().item():.3f} - {finite_upper.max().item():.3f} Å")
            self._mark_config_logged()
        
        # Store info for potential runtime logging in compute()
        # Note: pair_index and k are fixed values, so we don't log them here
        
        return pair_index, (k, lower_bounds, upper_bounds), None


class AmberGeometryFilterMixin:
    """
    Geometry filtering mixin for Amber force field potentials.
    
    Provides soft clamping and statistical tracking for physical constraints.
    This mixin ensures that extreme geometric configurations during diffusion
    don't cause numerical instabilities in Amber force field calculations.
    """
    
    def __init__(self, *args, **kwargs):
        """Initialize geometry filtering with statistics tracking."""
        # Don't call super().__init__() to avoid issues with object.__init__()
        # Statistics counters for summary reporting
        self.geometry_stats = {
            "distance_clamps": 0,
            "angle_clamps": 0,
            "total_distance_evaluations": 0,
            "total_angle_evaluations": 0,
            "summary_logged": False
        }
        
        # Filtering parameters
        self.min_distance = 1.0    # Minimum physical distance (Å)
        self.max_distance = 20.0   # Maximum physical distance (Å)
        self.min_angle = 0.087     # Minimum angle (5° in radians)
        self.max_angle = 3.054     # Maximum angle (175° in radians)
        self.min_sin_threshold = 0.087  # Minimum sin(angle) for numerical stability
    
    def _apply_distance_filtering(self, distances):
        """
        Apply soft clamping to distance values for numerical stability.
        
        Parameters
        ----------
        distances : torch.Tensor
            Distance values to filter
            
        Returns
        -------
        torch.Tensor
            Filtered distance values
        """
        self.geometry_stats["total_distance_evaluations"] += distances.numel()
        
        # Count how many values need clamping
        too_small = (distances < self.min_distance).sum().item()
        too_large = (distances > self.max_distance).sum().item()
        
        self.geometry_stats["distance_clamps"] += too_small + too_large
        
        # Apply soft clamping
        filtered_distances = torch.clamp(distances, min=self.min_distance, max=self.max_distance)
        
        return filtered_distances
    
    def _apply_angle_filtering(self, angles, sin_angles=None):
        """
        Apply soft clamping to angle values for numerical stability.
        
        Parameters
        ----------
        angles : torch.Tensor
            Angle values to filter (radians)
        sin_angles : torch.Tensor, optional
            sin(angle) values to filter
            
        Returns
        -------
        tuple
            (filtered_angles, filtered_sin_angles)
        """
        self.geometry_stats["total_angle_evaluations"] += angles.numel()
        
        # Count how many values need clamping
        too_small = (angles < self.min_angle).sum().item()
        too_large = (angles > self.max_angle).sum().item()
        
        self.geometry_stats["angle_clamps"] += too_small + too_large
        
        # Apply soft clamping to angles
        filtered_angles = torch.clamp(angles, min=self.min_angle, max=self.max_angle)
        
        # Apply soft clamping to sin(angles) if provided
        filtered_sin_angles = sin_angles
        if sin_angles is not None:
            # Ensure sin(angle) is not too close to zero for numerical stability
            filtered_sin_angles = torch.clamp(torch.abs(sin_angles), min=self.min_sin_threshold) * torch.sign(sin_angles)
        
        return filtered_angles, filtered_sin_angles
    
    def _log_geometry_summary(self):
        """Log summary statistics for geometry filtering."""
        if self.geometry_stats["summary_logged"] or not self.debug_enabled:
            return
            
        total_dist = self.geometry_stats["total_distance_evaluations"]
        total_angle = self.geometry_stats["total_angle_evaluations"]
        dist_clamps = self.geometry_stats["distance_clamps"]
        angle_clamps = self.geometry_stats["angle_clamps"]
        
        if total_dist > 0 or total_angle > 0:
            self._log_config("📐 Geometry Filtering Summary:")
            if total_dist > 0:
                clamp_rate = (dist_clamps / total_dist) * 100
                self._log_config(f"  Distance evaluations: {total_dist:,}, clamped: {dist_clamps:,} ({clamp_rate:.2f}%)")
            if total_angle > 0:
                clamp_rate = (angle_clamps / total_angle) * 100
                self._log_config(f"  Angle evaluations: {total_angle:,}, clamped: {angle_clamps:,} ({clamp_rate:.2f}%)")
            self._log_config(f"  Filtering range: distances [{self.min_distance:.1f}-{self.max_distance:.1f}Å], angles [{self.min_angle*180/3.14159:.0f}°-{self.max_angle*180/3.14159:.0f}°]")
            
        self.geometry_stats["summary_logged"] = True


class AmberBondPotential(HarmonicPotential, DistancePotential, AmberGeometryFilterMixin):
    """
    Amber bond constraints using harmonic potential.
    
    This potential applies harmonic oscillator constraints from Amber force field bond parameters. 
    It uses equilibrium distances and force constants directly from Amber topology files.
    
    Energy function: E = 0.5 * k * (r - r0)^2
    where r is current distance, r0 is equilibrium distance from Amber, 
    k is force constant derived from Amber parameters.
    """
    
    def __init__(self, parameters=None, debug_enabled=False):
        """Initialize AmberBondPotential with gradient capping capability."""
        super().__init__(parameters, debug_enabled)
        # Ensure AmberGeometryFilterMixin is also initialized
        if not hasattr(self, 'geometry_stats'):
            AmberGeometryFilterMixin.__init__(self, parameters, debug_enabled)
        self.previous_gradient = None   # Storage for previous gradient to handle NaN fallback
        self.max_gradient_norm = 1000.0 # Gradient capping threshold
    
    def compute_variable(self, coords, index, compute_gradient=False):
        """
        Compute distance variable with Amber-specific geometry filtering.
        
        Parameters
        ----------
        coords : torch.Tensor
            Atomic coordinates
        index : tuple
            Atom indices for distance calculation
        compute_gradient : bool
            Whether to compute gradients
            
        Returns
        -------
        torch.Tensor or tuple
            Distance values and gradients (if requested)
        """
        r_ij = coords.index_select(-2, index[0]) - coords.index_select(-2, index[1])
        r_ij_norm = torch.linalg.norm(r_ij, dim=-1)
        
        # 🆕 Apply Amber-specific geometry filtering for bond distances
        r_ij_norm = self._apply_distance_filtering(r_ij_norm)
        
        r_ij_norm = torch.clamp(r_ij_norm, min=1e-6)  # Additional numerical safety
        r_hat_ij = r_ij / r_ij_norm.unsqueeze(-1)
        
        if not compute_gradient:
            return r_ij_norm

        grad_i = r_hat_ij
        grad_j = -1 * r_hat_ij
        grad = torch.stack((grad_i, grad_j), dim=1)

        return r_ij_norm, grad
    
    def compute_gradient(self, coords, feats, parameters):
        """Compute gradient with numerical stability protection."""
        # Call parent compute_gradient to get raw gradients
        grad_atom = super().compute_gradient(coords, feats, parameters)
        
        # Apply gradient magnitude capping and NaN protection
        grad_atom = self._apply_gradient_protection(grad_atom)
        
        return grad_atom
    
    def _apply_gradient_protection(self, grad_atom):
        """Apply gradient capping and NaN protection for numerical stability."""
        if grad_atom is None:
            return grad_atom
            
        # Check for NaN or Inf values
        has_nan = torch.isnan(grad_atom).any()
        has_inf = torch.isinf(grad_atom).any()
        
        if has_nan or has_inf:
            if self.debug_enabled:
                self._log_debug(f"NaN/Inf detected in gradients (NaN: {has_nan}, Inf: {has_inf})")
            
            # Use previous gradient if available, otherwise zero
            if self.previous_gradient is not None:
                if self.debug_enabled:
                    self._log_debug("Using previous gradient as fallback")
                grad_atom = self.previous_gradient.clone()
            else:
                if self.debug_enabled:
                    self._log_debug("No previous gradient available, using zero gradient")
                grad_atom = torch.zeros_like(grad_atom)
        else:
            # Apply gradient magnitude capping
            grad_norm = torch.norm(grad_atom)
            
            if grad_norm > self.max_gradient_norm:
                if self.debug_enabled:
                    self._log_debug(f"Gradient capping applied: {grad_norm:.3f} → {self.max_gradient_norm}")
                grad_atom = grad_atom * (self.max_gradient_norm / grad_norm)
            
            # Store current gradient as previous for next iteration (only if it's valid)
            self.previous_gradient = grad_atom.clone().detach()
        
        return grad_atom
    
    def compute(self, coords, feats, parameters):
        # Call parent compute to get standard logging
        result = super().compute(coords, feats, parameters)
        
        # Add Amber bond-specific runtime information
        if (self.debug_enabled and result is not None and 
            hasattr(self, '_last_amber_runtime_log') and 
            self.call_count - self._last_amber_runtime_log >= 100):
            
            # Get actual distances for constraints
            index, args, com_args = self.compute_args(feats, parameters)
            if index.shape[1] > 0:
                # Compute actual distances
                r_ij = coords.index_select(-2, index[0]) - coords.index_select(-2, index[1])
                distances = torch.linalg.norm(r_ij, dim=-1)
                
                # Get equilibrium distances for comparison
                _, equilibrium = args
                
                # Analyze deviations from equilibrium
                deviations = torch.abs(distances - equilibrium)
                avg_deviation = deviations.mean().item()
                max_deviation = deviations.max().item()
                
                avg_distance = distances.mean().item()
                distance_std = distances.std().item()
                
                self._log_debug(
                    f"Amber Bond Analysis: Avg_dist={avg_distance:.3f}±{distance_std:.3f}Å, "
                    f"Avg_deviation={avg_deviation:.3f}Å, Max_deviation={max_deviation:.3f}Å"
                )
                
            self._last_amber_runtime_log = self.call_count
        
        # Initialize the runtime log counter if not exists
        if not hasattr(self, '_last_amber_runtime_log'):
            self._last_amber_runtime_log = 0
        
        # 🆕 Log geometry filtering summary
        self._log_geometry_summary()
            
        return result
    
    def compute_args(self, feats, parameters):
        """
        Compute arguments for Amber bond potential.
        
        Parameters
        ----------
        feats : dict
            Feature dictionary containing Amber bond constraints
        parameters : dict
            Potential parameters including force constant settings
            
        Returns
        -------
        tuple
            (pair_index, (k, equilibrium), None)
        """
        # Check if Amber bond features exist
        if "amber_bond_atom_index" not in feats:
            # Return empty tensors if no constraints are available
            device = next(iter(feats.values())).device if feats else 'cpu'
            return torch.empty((2, 0), device=device), (torch.empty(0, device=device), torch.empty(0, device=device)), None
            
        pair_index = feats["amber_bond_atom_index"][0]
        equilibrium = feats["amber_bond_equilibrium"][0].clone()
        weights = feats["amber_bond_weights"][0]
        
        # Log configuration info only on first call
        if not self._config_logged:
            num_constraints = pair_index.shape[1]
            self._log_config(f"Processing {num_constraints} Amber bond constraints")
            self._log_config(f"  Equilibrium distances: {equilibrium.min().item():.3f} - {equilibrium.max().item():.3f} Å")
            self._log_config(f"  Weights: {weights.min().item():.3f} - {weights.max().item():.3f}")
            self._log_config(f"  Base force constant: {parameters['base_force_constant']:.3f}")
            self._log_config('-' * 50)
            
        # Apply weights as force constants (Amber weights are already scaled)
        k = weights * parameters["base_force_constant"]
        
        # Log final configuration info only on first call
        if not self._config_logged:
            self._log_config(f"  Final force constants: {k.min().item():.3f} - {k.max().item():.3f}")
            self._mark_config_logged()
        
        return pair_index, (k, equilibrium), None


class AmberAnglePotential(HarmonicPotential, AnglePotential, AmberGeometryFilterMixin):
    """
    Amber angle constraints using harmonic potential.

    This potential applies harmonic oscillator constraints from Amber force field angle parameters. 
    It uses equilibrium angles and force constants directly from Amber topology files.

    Energy function: E = 0.5 * k * (θ - θ_0)^2
    where θ is current angle, θ_0 is equilibrium angle from Amber,
    k is force constant derived from Amber parameters.
    """

    def __init__(self, parameters=None, debug_enabled=False):
        """Initialize AmberAnglePotential with gradient capping capability."""
        super().__init__(parameters, debug_enabled)
        # Ensure AmberGeometryFilterMixin is also initialized
        if not hasattr(self, 'geometry_stats'):
            AmberGeometryFilterMixin.__init__(self, parameters, debug_enabled)
        self.previous_gradient = None   # Storage for previous gradient to handle NaN fallback
        self.max_gradient_norm = 100.0 # Gradient capping threshold
    
    def compute_variable(self, coords, index, compute_gradient=False):
        """
        Compute angle variable with Amber-specific geometry filtering.

        Parameters
        ----------
        coords : torch.Tensor
            Atomic coordinates with shape (..., N_atoms, 3)
        index : torch.Tensor
            Indices of three atoms with shape (3, N_constraints)
        compute_gradient : bool
            Whether to compute gradients

        Returns
        -------
        torch.Tensor or tuple
            Angle values and gradients (if requested)
        """
        # Get coordinates of the three atoms
        coords_1 = coords.index_select(-2, index[0])  # atom1 coordinates
        coords_2 = coords.index_select(-2, index[1])  # atom2 coordinates (center)
        coords_3 = coords.index_select(-2, index[2])  # atom3 coordinates

        # Compute vectors from center atom to other atoms
        vec_21 = coords_1 - coords_2  # vector from atom2 to atom1
        vec_23 = coords_3 - coords_2  # vector from atom2 to atom3

        # Compute vector norms
        norm_21 = torch.linalg.norm(vec_21, dim=-1, keepdim=True)
        norm_23 = torch.linalg.norm(vec_23, dim=-1, keepdim=True)

        # Normalize vectors
        vec_21_normalized = vec_21 / (norm_21 + 1e-8)
        vec_23_normalized = vec_23 / (norm_23 + 1e-8)

        # Compute cosine of angle using dot product
        cos_angle = torch.sum(vec_21_normalized * vec_23_normalized, dim=-1)

        # Clamp cosine to valid range [-1, 1] for numerical stability
        cos_angle = torch.clamp(cos_angle, -1.0 + 1e-6, 1.0 - 1e-6)

        # Compute angle in radians
        angle = torch.acos(cos_angle)
        
        # 🆕 Apply Amber-specific geometry filtering for angles
        sin_angle = torch.sin(angle) if compute_gradient else None
        angle, sin_angle = self._apply_angle_filtering(angle, sin_angle)

        if not compute_gradient:
            return angle

        # Use filtered sin_angle for gradient computation
        sin_angle = sin_angle.unsqueeze(-1)  # Add dimension for broadcasting
        sin_angle = torch.clamp(sin_angle, 1e-6, None)  # Additional safety

        # Gradient components
        grad_1 = (vec_23_normalized - cos_angle.unsqueeze(-1) * vec_21_normalized) / (norm_21.squeeze(-1).unsqueeze(-1) * sin_angle)
        grad_3 = (vec_21_normalized - cos_angle.unsqueeze(-1) * vec_23_normalized) / (norm_23.squeeze(-1).unsqueeze(-1) * sin_angle)
        grad_2 = -(grad_1 + grad_3)

        # Stack gradients: shape (..., 3, N_constraints, 3)
        grad = torch.stack((grad_1, grad_2, grad_3), dim=-3)

        return angle, grad
    
    def compute_gradient(self, coords, feats, parameters):
        """Compute gradient with numerical stability protection."""
        # Call parent compute_gradient to get raw gradients
        grad_atom = super().compute_gradient(coords, feats, parameters)
        
        # Apply gradient magnitude capping and NaN protection
        grad_atom = self._apply_gradient_protection(grad_atom)
        
        return grad_atom
    
    def _apply_gradient_protection(self, grad_atom):
        """Apply gradient capping and NaN protection for numerical stability."""
        if grad_atom is None:
            return grad_atom
            
        # Check for NaN or Inf values
        has_nan = torch.isnan(grad_atom).any()
        has_inf = torch.isinf(grad_atom).any()
        
        if has_nan or has_inf:
            if self.debug_enabled:
                self._log_debug(f"NaN/Inf detected in gradients (NaN: {has_nan}, Inf: {has_inf})")
            
            # Use previous gradient if available, otherwise zero
            if self.previous_gradient is not None:
                if self.debug_enabled:
                    self._log_debug("Using previous gradient as fallback")
                grad_atom = self.previous_gradient.clone()
            else:
                if self.debug_enabled:
                    self._log_debug("No previous gradient available, using zero gradient")
                grad_atom = torch.zeros_like(grad_atom)
        else:
            # Apply gradient magnitude capping
            grad_norm = torch.norm(grad_atom)
            
            if grad_norm > self.max_gradient_norm:
                if self.debug_enabled:
                    self._log_debug(f"Gradient capping applied: {grad_norm:.3f} → {self.max_gradient_norm}")
                grad_atom = grad_atom * (self.max_gradient_norm / grad_norm)
            
            # Store current gradient as previous for next iteration (only if it's valid)
            self.previous_gradient = grad_atom.clone().detach()
        
        return grad_atom

    def compute(self, coords, feats, parameters):        
        # Call parent compute to get standard logging
        result = super().compute(coords, feats, parameters)

        # Add Amber angle-specific runtime information
        if (self.debug_enabled and result is not None and
            hasattr(self, '_last_amber_angle_runtime_log') and
            self.call_count - self._last_amber_angle_runtime_log >= 100):

            # Get actual angles for constraints
            index, args, com_args = self.compute_args(feats, parameters)
            if index.shape[1] > 0:
                # Compute actual angles using AnglePotential's compute_variable
                angles = self.compute_variable(coords, index, compute_gradient=False)

                # Get equilibrium angles for comparison
                _, equilibrium = args

                # Analyze deviations from equilibrium
                deviations = torch.abs(angles - equilibrium)
                avg_deviation = deviations.mean().item()
                max_deviation = deviations.max().item()

                avg_angle = angles.mean().item()
                angle_std = angles.std().item()

                self._log_debug(
                    f"Amber Angle Analysis: Avg_angle={avg_angle*180/3.14159:.1f}°±{angle_std*180/3.14159:.1f}°, "
                    f"Avg_deviation={avg_deviation*180/3.14159:.1f}°, Max_deviation={max_deviation*180/3.14159:.1f}°"
                )

            self._last_amber_angle_runtime_log = self.call_count

        # Initialize the runtime log counter if not exists
        if not hasattr(self, '_last_amber_angle_runtime_log'):
            self._last_amber_angle_runtime_log = 0
        
        # 🆕 Log geometry filtering summary
        self._log_geometry_summary()

        return result

    def compute_args(self, feats, parameters):
        """
        Compute arguments for Amber angle potential.

        Parameters
        ----------
        feats : dict
            Feature dictionary containing Amber angle constraints
        parameters : dict
            Potential parameters including force constant settings

        Returns
        -------
        tuple
            (triplet_index, (k, equilibrium), None)
        """
        # Check if Amber angle features exist
        if "amber_angle_atom_index" not in feats:
            # Return empty tensors if no constraints are available
            device = next(iter(feats.values())).device if feats else 'cpu'
            return torch.empty((3, 0), device=device), (torch.empty(0, device=device), torch.empty(0, device=device)), None

        triplet_index = feats["amber_angle_atom_index"][0]  # (3, N) tensor
        equilibrium_angles = feats["amber_angle_equilibrium_angle"][0].clone()  # radians
        equilibrium_distances = feats["amber_angle_equilibrium_distance"][0].clone()  # Angstroms (for reference)
        weights = feats["amber_angle_weights"][0]  # force constants

        # Log configuration info only on first call
        if not self._config_logged:
            num_constraints = triplet_index.shape[1]
            self._log_config(f"Processing {num_constraints} Amber angle constraints")
            self._log_config(f"  Equilibrium angles: {equilibrium_angles.min().item()*180/3.14159:.1f} - {equilibrium_angles.max().item()*180/3.14159:.1f} rad")
            self._log_config(f"  Equilibrium distances (1-3): {equilibrium_distances.min().item():.3f} - {equilibrium_distances.max().item():.3f} Å")
            self._log_config(f"  Weights (force constants): {weights.min().item():.3f} - {weights.max().item():.3f} kcal/mol/rad²")
            self._log_config(f"  Base force constant: {parameters['base_force_constant']:.3f}")
            self._log_config('-' * 50)

        # Apply weights as force constants (Amber weights are already scaled)
        k = weights * parameters["base_force_constant"]

        # Log final configuration info only on first call
        if not self._config_logged:
            self._log_config(f"  Final force constants: {k.min().item():.3f} - {k.max().item():.3f}")
            self._mark_config_logged()

        return triplet_index, (k, equilibrium_angles), None


class HarmonicDistancePotential(HarmonicPotential, DistancePotential):
    def compute(self, coords, feats, parameters):
        # Call parent compute to get standard logging
        result = super().compute(coords, feats, parameters)
        
        # Add Harmonic-specific runtime information
        if (self.debug_enabled and result is not None and 
            hasattr(self, '_last_harmonic_runtime_log') and 
            self.call_count - self._last_harmonic_runtime_log >= 100):
            
            # Get actual distances for constraints
            index, args, com_args = self.compute_args(feats, parameters)
            if index.shape[1] > 0:
                # Compute actual distances
                r_ij = coords.index_select(-2, index[0]) - coords.index_select(-2, index[1])
                distances = torch.linalg.norm(r_ij, dim=-1)
                
                # Get equilibrium distances for comparison
                _, equilibrium = args
                
                # Analyze deviations from equilibrium
                deviations = torch.abs(distances - equilibrium)
                avg_deviation = deviations.mean().item()
                max_deviation = deviations.max().item()
                
                avg_distance = distances.mean().item()
                distance_std = distances.std().item()
                
                self._log_debug(
                    f"Harmonic Analysis: Avg_dist={avg_distance:.3f}±{distance_std:.3f}Å, "
                    f"Avg_deviation={avg_deviation:.3f}Å, Max_deviation={max_deviation:.3f}Å"
                )
                
            self._last_harmonic_runtime_log = self.call_count
        
        # Initialize the runtime log counter if not exists
        if not hasattr(self, '_last_harmonic_runtime_log'):
            self._last_harmonic_runtime_log = 0
            
        return result
    
    def compute_args(self, feats, parameters):
        """
        Compute arguments for harmonic distance potential.
        
        Parameters
        ----------
        feats : dict
            Feature dictionary containing harmonic distance constraints
        parameters : dict
            Potential parameters including force constant settings
            
        Returns
        -------
        tuple
            (pair_index, (k, equilibrium), None)
        """
        # Check if harmonic distance features exist
        if "harmonic_distance_atom_index" not in feats:
            # Return empty tensors if no constraints are available
            device = next(iter(feats.values())).device if feats else 'cpu'
            return torch.empty((2, 0), device=device), (torch.empty(0, device=device), torch.empty(0, device=device)), None
            
        pair_index = feats["harmonic_distance_atom_index"][0]
        equilibrium = feats["harmonic_distance_equilibrium"][0].clone()
        weights = feats["harmonic_distance_weights"][0]
        
        # Log configuration info only on first call
        if not self._config_logged:
            num_constraints = pair_index.shape[1]
            self._log_config(f"Processing {num_constraints} harmonic distance constraints")
            self._log_config(f"  Equilibrium distances: {equilibrium.min().item():.3f} - {equilibrium.max().item():.3f} Å")
            self._log_config(f"  Weights: {weights.min().item():.3f} - {weights.max().item():.3f}")
            self._log_config(f"  Base force constant: {parameters['base_force_constant']:.3f}")

        # Apply weights as force constants
        k = weights * parameters["base_force_constant"]
        
        # Log final configuration info only on first call
        if not self._config_logged:
            self._log_config(f"  Final force constants: {k.min().item():.3f} - {k.max().item():.3f}")
            self._log_config('-' * 50)
            self._mark_config_logged()
        
        return pair_index, (k, equilibrium), None



def get_potentials(debug_enabled=True):
    """
    Get list of potentials with configurable logging.
    
    Parameters
    ----------
    debug_enabled : bool, default=False
        Enable debug logging for all potentials
        
    Returns
    -------
    list[Potential]
        List of configured potential objects
    """
    potentials = [
        SymmetricChainCOMPotential(
            parameters={
                "guidance_interval": 4,
                "guidance_weight": 0.5,
                "resampling_weight": 0.5,
                "buffer": ExponentialInterpolation(start=1.0, end=5.0, alpha=-2.0),
            },
            debug_enabled=debug_enabled
        ),
        VDWOverlapPotential(
            parameters={
                "guidance_interval": 5,
                "guidance_weight": PiecewiseStepFunction(
                    thresholds=[0.4], values=[0.125, 0.0]
                ),
                "resampling_weight": PiecewiseStepFunction(
                    thresholds=[0.6], values=[0.01, 0.0]
                ),
                "buffer": 0.225,
            },
            debug_enabled=debug_enabled
        ),
        ConnectionsPotential(
            parameters={
                "guidance_interval": 1,
                "guidance_weight": 0.15,
                "resampling_weight": 1.0,
                "buffer": 2.0,
            },
            debug_enabled=debug_enabled
        ),        
        PoseBustersPotential(
            parameters={
                "guidance_interval": 1,
                "guidance_weight": 1,
                "resampling_weight": 0.1,
                "bond_buffer": 0.20,
                "angle_buffer": 0.20,
                "clash_buffer": 0.15,
            },
            debug_enabled=debug_enabled
        ),
        ChiralAtomPotential(
            parameters={
                "guidance_interval": 1,
                "guidance_weight": 0.10,
                "resampling_weight": 1.0,
                "buffer": 0.52360,
            },
            debug_enabled=debug_enabled
        ),
        StereoBondPotential(
            parameters={
                "guidance_interval": 1,
                "guidance_weight": 0.05,
                "resampling_weight": 1.0,
                "buffer": 0.52360,
            },
            debug_enabled=debug_enabled
        ),
        PlanarBondPotential(
            parameters={
                "guidance_interval": 1,
                "guidance_weight": 0.05,
                "resampling_weight": 1.0,
                "buffer": 0.26180,
            },
            debug_enabled=debug_enabled
        ),
        # ==================== #
        #  Code Modification   #
        # ==================== #
        # MinDistancePotential( 
        #     parameters={
        #         "guidance_interval": 1,
        #         "guidance_weight": 0.15,
        #         "resampling_weight": 10,
        #         "buffer": 0.5,
        #     },
        #     debug_enabled=debug_enabled
        # ),
        NMRDistancePotential( 
            parameters={
                "guidance_interval": 2,
                "guidance_weight": ExponentialInterpolation(
                    start=0.05,
                    end=0.15,
                    alpha=-2.0
                ),                
                "resampling_weight": 1.0,
                "lower_buffer": 0.1,
                "upper_buffer": 0.1,
                "base_force_constant": 1.0,
            },
            debug_enabled=debug_enabled
        ),
        AmberBondPotential(
            parameters={
                "guidance_interval": 2,
                "guidance_weight": ExponentialInterpolation(
                    start=0.00,
                    end=0.1,
                    alpha=5.0
                ),
                "resampling_weight": 1.0,
                "base_force_constant": 1.0,  
            },
            debug_enabled=debug_enabled
        ),
        AmberAnglePotential(
            parameters={
                "guidance_interval": 2,
                "guidance_weight": ExponentialInterpolation(
                    start=0.00,
                    end=0.05,
                    alpha=-2.0
                ),
                "resampling_weight": 1.0,
                "base_force_constant": 0.1,  
            },
            debug_enabled=debug_enabled
        ),    
    ]
    
    print("===================================================")
    print("========  Potential Configuration Summary  ========")
    print(f'{"Potential Name":28} {"DEBUG":5} {"CONFIG_LOGGED":10}')
    print('-' * 51)
    for potential in potentials:
        stats = potential.get_stats()
        print(f"{stats['potential_name']:28} {stats['debug_enabled']:3} {stats['config_logged']:5}")
    print("===================================================\n")
    return potentials




@dataclass
class GuidanceConfig:
    """Guidance configuration."""

    potentials: Optional[list[Potential]] = None
    guidance_update: Optional[bool] = None
    num_guidance_gd_steps: Optional[int] = None
    guidance_gd_step_size: Optional[int] = None
    fk_steering: Optional[bool] = None
    fk_resampling_interval: Optional[int] = 1
    fk_lambda: Optional[float] = 1.0
    fk_method: Optional[str] = None
    fk_batch_size: Optional[int] = 2
