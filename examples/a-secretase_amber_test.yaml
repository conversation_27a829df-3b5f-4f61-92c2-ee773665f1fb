version: 1
sequences:
  - protein:
      id: A
      sequence: EKNTCQLYIQTDHLFFKYYGTREAVIAQISSHVKAIDTIYQTTDFSGIRNISFMVKRIRINTTADEKDPTNPFRFPNIGVEKFLELNSEQNHDDYCLAYVFTDRDFDDGVLGLAWVGAPSGSSGGICEKSKLYSDGKKKSLNTGIITVQNYGSHVPPKVSHITFAHEVGHNFGSPHDSGTECTPGESKNLGQKENGNYIMYARATSGDKLNNNKFSLCSIRNISQVLEKKRNNCFVESGQPICGNGMVEQGEECDCGYSDQCKDECCFDANQPEGRKCKLKPGKQCSPSQGPCCTAQCAFKSKSEKCRDDSDCAREGICNGFTALCPASDPKPNFTDCNRHTQVCINGQCAGSICEKYGLEECTCASSDGKDDKELCHVCCMKKMDPSTCASTGSVQWSRHFSGRTITLQPGSPCNDFRGYCDVFMRCRLVDADGPLARLKKAIFSPELYENI
      msa: ./examples/msa/a-secretase_101.a3m
      amber:
        force_field: "ff14SB"
        constraint_types: ["bonds", "angles", "dihedrals"]
      template:
        structure: "examples/8esv_A_tmpl.cif"
        chain_id: "A"
  - protein:
      id: B
      sequence: ARFSYLWLKFSLIIYSTVFWLIGALVLSVGIYAEVERQKYKTLESAFLAPAIILILLGVVMFMVSFIGVLASLRDNLYLLQAFMYILGICLIMELIGGVVALTFRNQTIDFLNDNIRRGIENYYDDLDFKNIMDFVQKKFKCCGGEDYRDWSKNQYHDCSAPGPLACGVPYTCCIRNTTEVVNTMCGYKTIDKERFSVQDVIYVRGCTNAVIIWFMDNYTIMAGILLGILLPQFLGVLLTLLYITRVEDIIMEHS
      msa: ./examples/msa/a-secretase_102.a3m
      amber:
        force_field: "ff14SB"
        constraint_types: ["bonds", "angels"]  # Only bonds for chain B
      template:
        structure: "examples/8esv_B_tmpl.cif"
        chain_id: "B" 